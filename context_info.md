Comprehensive System Design Write-Up for GovPSS and IPMS
Overview
This write-up consolidates details from prior discussions about two systems: GovPSS, a Human Resource Management System tailored for government institutions in Papua New Guinea, and IPMS (Integrated Progress Monitoring System), a platform for monitoring human resources, organizational plans, budgets, and asset usage linked to Workplans and Annual Activity Plans. Both systems leverage CodeIgniter 4 and MySQL, with a focus on RESTful architecture, user-friendly interfaces, and role-based functionalities. This document covers their features, technical designs, user interfaces, and development considerations, drawing from the described requirements and workflows.
GovPSS: Human Resource Management System
Purpose and Context
GovPSS is designed for government organizations, such as the East Sepik Provincial Administration, to streamline HR processes, including job applications, employee management, and administrative tasks. It emphasizes efficiency, transparency, and regional relevance for Papua New Guinea’s public sector.
Key Features

Job Application Process:
Account creation and user authentication for applicants.
Job listings by organization, displaying 20 random positions by default, with downloadable job descriptions (JDs) in PDF format.
Application submission with a 24-hour revocation period.
Administrator-managed pre-selection stages (screening, rating) using tables like appx_pre_selection_process.
Interview management with tracking via interview_data and interview_pool tables, storing scores and statuses.


Employee Portal:
Features for employees to view salaries, benefits, and personal details.
CRUD operations for employee data, managed via controllers like SalariesController and models like orgModel.


Administrative Portal:
Tools for managing applications, interviews, and final selections.
Role-based access for super admins, admins, editors, and front desk staff.


Correspondence Tracking:
A system inspired by Dakoii, supporting document management, referral, and action workflows.
Clean URLs and file storage in the filesystem for documents.



Technical Specifications

Framework: CodeIgniter 4.6.0 with MVC architecture.
Database: MySQL with tables such as:
users: Stores user details (email, password, role).
organizations: Manages organization data.
correspondence: Tracks document status and workflows.
appx_pre_selection_process, interview_data, interview_pool: Support recruitment processes.


Controllers:
HomeController: Manages the landing page.
DashboardController: Handles user and admin dashboards.


Frontend: Bootstrap 5 for responsive design.
Storage: Filesystem for document storage, with clean URL routing.

Benefits

Streamlines recruitment with a data-driven approach.
Enhances transparency through clear application and interview tracking.
Supports multiple organizations with role-based access.
Tailored for Papua New Guinea’s government context, proven by clients like East Sepik Provincial Administration.

How It Works

Applicants:
Create accounts, browse job listings, and apply with uploaded documents.
View application statuses and revoke submissions within 24 hours.


Administrators:
Manage job postings, screen applications, and rate candidates.
Conduct interviews (online or face-to-face) and finalize selections using data-driven tools.


Employees:
Access personal data, salaries, and benefits via the employee portal.



IPMS: Integrated Progress Monitoring System (V1)
Purpose and Context
IPMS is a comprehensive system for monitoring human resources, organizational plans, budgets, and assets, all tied to Workplans and Annual Activity Plans. It supports government officers in planning, tracking, and reporting activities, with automated form generation for financial claims (e.g., FF3, FF4, Alignment Sheets).
Key Features

Workplan and Activity Management:
Officers create Workplans with Activities and Projects, linked to Budget Books, Corporate Plans, and Development Plans.
Legislated Activities assigned by administrators, categorized by groups and codes.


Financial Claim Management:
Automated generation of FF3, FF4, and Alignment Sheets using DomPDF.
Claims linked to organizational budgets and plans.


User Roles and Navigation:
Roles: Officer, Group Admin, Supervisor, ARO, Fund Manager, Administrator.
Button-driven, mobile app-like interface with navigation options like:
My Workplan Activities: View and manage assigned tasks.
Financial Claims: Submit and track claims.
Administrator Settings: Manage users, structures, and plans.




Modules:
User Management: CRUD operations for users and roles.
Structure Management: Organizes groups, departments, and codes.
Plans Management: Handles Workplans, Budget Books, and Corporate Plans.


RESTful APIs:
Endpoints for CRUD operations across modules, ensuring scalability.



Technical Specifications

Framework: CodeIgniter 4 with RESTful architecture.
Database: MySQL with tables including:
users: Stores user details (email, password, role).
structures: Manages organizational groups and codes.
plans: Tracks Workplans, Activities, and Projects.
budgets: Links financial data to plans.


Frontend: Bootstrap 5 for a responsive, button-driven UI.
Tools:
DomPDF: For generating PDF forms (FF3, FF4, Alignment Sheets).
Augment Code and Cursor: Support development and code generation.


Security:
Role-based access control.
Secure API endpoints with authentication.



UI Design

Navigation: Button-driven interface, replacing traditional menus for a mobile app-like experience.
Home Page: Displays role-specific options (e.g., My Workplan Activities, Financial Claims).
My Profile: Allows users to view and edit personal details.
Administrator Settings: Manages users, structures, and plans.


Design Principles:
Simplicity and responsiveness for all devices.
Role-based access to ensure relevant features are accessible.
Consistent navigation across pages.



Development Tasks

Setup:
Configure CodeIgniter 4 environment and MySQL database.
Define database schema with relationships and constraints.


Backend:
Develop controllers and models for User, Structure, and Plans Management.
Implement RESTful APIs for CRUD operations.


Frontend:
Build responsive UI with Bootstrap 5.
Implement button-driven navigation and role-based dashboards.


Security:
Add authentication and authorization layers.
Secure file storage and API endpoints.



Workflow

Plan Mode: Define features, design UI, and create technical plans (e.g., flowcharts).
Act Mode: Execute development tasks, test APIs, and deploy UI.
Memory Bank:
Files like projectbrief.md, productContext.md, and activeContext.md capture project intelligence.
.clinerules file documents patterns and preferences (e.g., button-driven UI, DomPDF usage).



Additional Considerations

Interview Management (from GovPSS context):
Supports online and face-to-face interviews.
Example: 13 candidates for online interviews, 14 for face-to-face, with contact details (phone, email) stored in tables.
Landscape-oriented tables for clarity, with numbered candidate lists.


Planning Process:
A four-step vibe planning process (using tools like Manis and Claude) ensures clarity:
Architect the app (define features and modules).
Design screens (UI layouts, button-driven navigation).
Create technical plans (system diagrams, database schemas).
Execute and refine (code, test, deploy).




Regional Relevance:
Both systems are tailored for Papua New Guinea’s government context, supporting organizations like the East Sepik Provincial Administration.



Conclusion
GovPSS and IPMS are robust, user-centric systems built on CodeIgniter 4 and MySQL, designed to meet the specific needs of government institutions. GovPSS streamlines HR and recruitment processes, while IPMS enhances planning, budgeting, and activity monitoring. Their button-driven, role-based interfaces ensure accessibility, and their RESTful architectures support scalability. By leveraging tools like DomPDF, Bootstrap 5, and a structured planning process, these systems deliver efficient, transparent, and regionally relevant solutions.
